/**
 * uniCloud云函数测试代码 - IMS字幕烧录
 * 可以直接在uniCloud云函数中运行
 */

'use strict';

const Core = require('@alicloud/pop-core');

exports.main = async (event, context) => {
  console.log('🚀 开始测试IMS字幕烧录功能');
  
  // 配置信息
  const config = {
    accessKeyId: "LTAI5t5tLkUwyQVg34k3ARFf",
    accessKeySecret: "******************************",
    regionId: "cn-shanghai",
    outputBucket: "video--tanslate"
  };

  // 测试素材
  const testData = {
    videoUrl: "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/video/task_688f00eb862066586c754d79_1754202351081.mp4",
    subtitleUrl: "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/subtitle/task_688f00eb862066586c754d79_translated_1754202381204.srt", // 修正为https
    taskId: "test_" + Date.now()
  };

  try {
    // 创建IMS客户端
    const imsClient = new Core({
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
      endpoint: `https://ice.${config.regionId}.aliyuncs.com`,
      apiVersion: "2020-11-09",
    });

    console.log('✅ IMS客户端创建成功');

    // 构建Timeline配置
    const timeline = {
      VideoTracks: [
        {
          VideoTrackClips: [
            {
              MediaURL: testData.videoUrl,
              TimelineIn: 0
            }
          ]
        }
      ],
      SubtitleTracks: [
        {
          SubtitleTrackClips: [
            {
              Type: "Subtitle",
              SubType: "srt",
              FileURL: testData.subtitleUrl,
              FontSize: 12,
              FontColor: "#FFD700", // 金黄色
              Alignment: "BottomCenter",
              Y: 0.85
            }
          ]
        }
      ]
    };

    // 构建输出配置
    const outputMediaConfig = {
      MediaURL: `https://${config.outputBucket}.oss-${config.regionId}.aliyuncs.com/test-output/${testData.taskId}.mp4`
    };

    // 构建IMS请求参数
    const imsParams = {
      Timeline: JSON.stringify(timeline),
      OutputMediaTarget: "oss-object",
      OutputMediaConfig: JSON.stringify(outputMediaConfig),
      UserData: JSON.stringify({
        taskId: testData.taskId,
        action: "burn_subtitle",
        testMode: true
      }),
      Source: "OpenAPI"
    };

    console.log('📋 请求参数构建完成');
    console.log('📹 输入视频:', testData.videoUrl);
    console.log('📝 字幕文件:', testData.subtitleUrl);
    console.log('🎯 输出地址:', outputMediaConfig.MediaURL);

    // 调用IMS API
    console.log('🚀 正在调用SubmitMediaProducingJob...');
    const response = await imsClient.request("SubmitMediaProducingJob", imsParams, {
      method: "POST",
    });

    console.log('✅ API调用成功!');
    
    const { JobId, MediaId, ProjectId } = response;
    
    if (JobId) {
      console.log('🆔 任务提交成功:');
      console.log('- JobId:', JobId);
      console.log('- MediaId:', MediaId);
      console.log('- ProjectId:', ProjectId);
      
      return {
        code: 200,
        message: "IMS任务提交成功",
        data: {
          jobId: JobId,
          mediaId: MediaId,
          projectId: ProjectId,
          taskId: testData.taskId,
          outputUrl: outputMediaConfig.MediaURL,
          timeline: timeline
        }
      };
      
    } else {
      throw new Error("响应中缺少JobId");
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    let errorMessage = error.message;
    let errorDetails = {};
    
    if (error.data) {
      console.error('错误详情:', JSON.stringify(error.data, null, 2));
      errorDetails = error.data;
    }
    
    if (error.code) {
      errorMessage = `${error.code}: ${error.message}`;
    }

    return {
      code: 500,
      message: "IMS测试失败",
      error: errorMessage,
      details: errorDetails,
      testData: testData
    };
  }
};

/**
 * 查询任务状态的云函数
 * 调用参数: { jobId: "xxx" }
 */
exports.queryStatus = async (event, context) => {
  const { jobId } = event;
  
  if (!jobId) {
    return {
      code: 400,
      message: "缺少jobId参数"
    };
  }

  const config = {
    accessKeyId: "LTAI5t5tLkUwyQVg34k3ARFf",
    accessKeySecret: "******************************",
    regionId: "cn-shanghai"
  };

  try {
    const imsClient = new Core({
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
      endpoint: `https://ice.${config.regionId}.aliyuncs.com`,
      apiVersion: "2020-11-09",
    });

    console.log('🔍 查询任务状态:', jobId);

    const response = await imsClient.request("GetMediaProducingJob", {
      JobId: jobId
    }, {
      method: "POST"
    });

    const { MediaProducingJob } = response;
    
    if (MediaProducingJob) {
      const { Status, CompleteTime, MediaURL, MediaId, ErrorMessage } = MediaProducingJob;
      
      console.log('📊 任务状态:', Status);
      
      return {
        code: 200,
        message: "查询成功",
        data: {
          jobId: jobId,
          status: Status,
          completeTime: CompleteTime,
          mediaURL: MediaURL,
          mediaId: MediaId,
          errorMessage: ErrorMessage
        }
      };
      
    } else {
      throw new Error("无法获取任务信息");
    }

  } catch (error) {
    console.error('❌ 查询失败:', error);
    
    return {
      code: 500,
      message: "查询任务状态失败",
      error: error.message,
      jobId: jobId
    };
  }
};
