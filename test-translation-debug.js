/**
 * 调试翻译问题的测试脚本
 * 用于手动触发翻译并观察详细日志
 */

console.log("🔧 翻译调试测试脚本");
console.log("=" * 50);

// 模拟云函数调用的测试函数
async function testTranslationCall(taskId) {
  console.log(`🧪 测试翻译调用，taskId: ${taskId}`);
  
  try {
    // 这里需要替换为实际的云函数调用
    // 在实际环境中，你需要通过 uniCloud 控制台或其他方式调用
    console.log("📡 准备调用 process-video-task 云函数...");
    console.log("📋 调用参数：", {
      name: "process-video-task",
      data: {
        taskId: taskId,
        action: "translate"
      }
    });
    
    // 模拟调用结果
    console.log("⚠️  注意：这是模拟调用，请在 uniCloud 控制台中手动执行");
    console.log("⚠️  或者通过前端页面触发实际的翻译调用");
    
  } catch (error) {
    console.error("❌ 测试调用失败：", error);
  }
}

// 调试检查清单
console.log("🔍 调试检查清单：");
console.log("");

console.log("1. ✅ 检查任务状态");
console.log("   - 确认任务状态为 'translating'");
console.log("   - 确认 subtitleOssUrl 存在且可访问");
console.log("   - 确认 translationStarted 字段状态");
console.log("");

console.log("2. ✅ 检查云函数日志");
console.log("   - 查看 process-video-task 云函数日志");
console.log("   - 查找 '[前台模式]' 相关日志");
console.log("   - 查找 '[translateSubtitleGPT]' 相关日志");
console.log("");

console.log("3. ✅ 检查可能的失败点");
console.log("   - 云函数调用超时");
console.log("   - 数据库连接问题");
console.log("   - 任务信息获取失败");
console.log("   - 参数传递错误");
console.log("");

console.log("4. ✅ 手动测试步骤");
console.log("   - 在 uniCloud 控制台找到 process-video-task 云函数");
console.log("   - 使用以下参数手动调用：");
console.log("     {");
console.log('       "taskId": "你的任务ID",');
console.log('       "action": "translate"');
console.log("     }");
console.log("   - 观察执行日志和返回结果");
console.log("");

console.log("5. ✅ 关键日志标识");
console.log("   - 🔄 [前台模式] 准备调用翻译功能");
console.log("   - 🔄 [translateSubtitleGPT] 开始GPT字幕翻译");
console.log("   - 📋 [translateSubtitleGPT] 正在获取任务信息");
console.log("   - ✅ [前台模式] 翻译功能调用成功");
console.log("   - ❌ [前台模式] 翻译功能调用失败");
console.log("");

console.log("🎯 最可能的问题：");
console.log("1. 云函数调用本身失败（超时、内存不足等）");
console.log("2. 任务信息获取失败（数据库连接问题）");
console.log("3. 参数传递问题（taskId 不正确）");
console.log("4. 云函数权限问题");
console.log("");

console.log("🔧 建议的调试步骤：");
console.log("1. 先检查云函数日志，确认是否有调用记录");
console.log("2. 手动在控制台调用 process-video-task");
console.log("3. 检查数据库中任务的实际状态");
console.log("4. 验证 taskId 是否正确");

// 如果提供了 taskId 参数，执行测试
const taskId = process.argv[2];
if (taskId) {
  console.log("");
  console.log("🧪 执行测试...");
  testTranslationCall(taskId);
} else {
  console.log("");
  console.log("💡 使用方法：node test-translation-debug.js <taskId>");
}
