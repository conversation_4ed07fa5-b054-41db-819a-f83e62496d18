/**
 * Serverless音频字幕生成器
 * 支持英文音频翻译成多语言字幕
 * 使用 whisper-large-v3 模型
 * 兼容 Vercel、Netlify、AWS Lambda 等serverless平台
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 语言映射
const LANGUAGE_MAP = {
    'zh': '中文',
    'ja': '日文', 
    'ko': '韩文',
    'fr': '法文',
    'de': '德文',
    'es': '西班牙文',
    'it': '意大利文',
    'pt': '葡萄牙文',
    'ru': '俄文',
    'ar': '阿拉伯文',
    'hi': '印地文',
    'th': '泰文',
    'vi': '越南文'
};

/**
 * 下载音频文件到内存
 */
async function downloadAudioToMemory(audioUrl) {
    return new Promise((resolve, reject) => {
        const url = new URL(audioUrl);
        const client = url.protocol === 'https:' ? https : http;
        
        console.log(`📥 正在下载音频: ${audioUrl}`);
        
        const request = client.get(audioUrl, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`下载失败: HTTP ${response.statusCode}`));
                return;
            }
            
            const chunks = [];
            let totalSize = 0;
            
            response.on('data', (chunk) => {
                chunks.push(chunk);
                totalSize += chunk.length;
                
                // 检查文件大小限制 (25MB)
                if (totalSize > 25 * 1024 * 1024) {
                    reject(new Error('音频文件超过25MB限制'));
                    return;
                }
            });
            
            response.on('end', () => {
                const audioBuffer = Buffer.concat(chunks);
                console.log(`✅ 音频下载完成，大小: ${(totalSize/1024/1024).toFixed(1)}MB (存储在内存中)`);
                resolve(audioBuffer);
            });
            
            response.on('error', reject);
        });
        
        request.on('error', reject);
        request.setTimeout(30000, () => {
            request.destroy();
            reject(new Error('下载超时'));
        });
    });
}

/**
 * 调用Whisper API进行语音转录
 */
async function transcribeAudio(audioBuffer, apiKey, baseUrl, sourceLanguage = 'en') {
    const FormData = require('form-data');
    
    console.log('🎤 正在转录音频...');
    
    const form = new FormData();
    form.append('file', audioBuffer, {
        filename: 'audio.mp3',
        contentType: 'audio/mpeg'
    });
    form.append('model', 'whisper-large-v3');
    form.append('response_format', 'verbose_json');
    form.append('temperature', '0.0');
    form.append('language', sourceLanguage);
    
    const options = {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            ...form.getHeaders()
        }
    };
    
    return new Promise((resolve, reject) => {
        const url = new URL(`${baseUrl}/v1/audio/transcriptions`);
        const client = url.protocol === 'https:' ? https : http;
        
        const request = client.request(url, options, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                try {
                    if (response.statusCode !== 200) {
                        reject(new Error(`Whisper API错误: ${response.statusCode} - ${data}`));
                        return;
                    }
                    
                    const result = JSON.parse(data);
                    console.log(`✅ 转录完成 (时长: ${result.duration || 'N/A'}秒)`);
                    
                    // 保存Whisper API响应
                    const timestamp = Date.now();
                    console.log('🔍 Whisper API原始响应:', JSON.stringify(result, null, 2));
                    
                    resolve({
                        whisperResult: result,
                        whisperResponse: {
                            timestamp,
                            data: result
                        }
                    });
                } catch (error) {
                    reject(new Error(`解析Whisper响应失败: ${error.message}`));
                }
            });
        });
        
        request.on('error', reject);
        request.setTimeout(120000, () => {
            request.destroy();
            reject(new Error('Whisper API请求超时'));
        });
        
        form.pipe(request);
    });
}

/**
 * 使用GPT批量翻译字幕段落
 */
async function translateSegmentsBatch(segments, apiKey, baseUrl, targetLanguage = 'zh') {
    if (!segments || segments.length === 0) {
        return [];
    }
    
    console.log(`🔄 正在批量翻译字幕内容到${targetLanguage.toUpperCase()}...`);
    
    // 构建编号的英文文本
    const englishTexts = segments.map((segment, index) => 
        `${index + 1}. ${segment.text.trim()}`
    ).filter(text => text.length > 3); // 过滤掉空文本
    
    if (englishTexts.length === 0) {
        return segments;
    }
    
    const combinedText = englishTexts.join('\n');
    const targetLangName = LANGUAGE_MAP[targetLanguage] || targetLanguage.toUpperCase();
    
    const payload = {
        model: 'gpt-3.5-turbo',
        messages: [
            {
                role: 'system',
                content: `你是一个专业的多语言翻译专家。请将用户提供的编号英文文本逐行翻译成自然流畅的${targetLangName}，保持原意不变。请保持相同的编号格式，每行一个翻译结果。`
            },
            {
                role: 'user',
                content: `请将以下编号的英文文本翻译成${targetLangName}，保持编号格式：\n\n${combinedText}`
            }
        ],
        temperature: 0.3,
        max_tokens: 2000
    };
    
    return new Promise((resolve, reject) => {
        const url = new URL(`${baseUrl}/v1/chat/completions`);
        const client = url.protocol === 'https:' ? https : http;
        
        const postData = JSON.stringify(payload);
        
        const options = {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const request = client.request(url, options, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                try {
                    if (response.statusCode !== 200) {
                        reject(new Error(`GPT API错误: ${response.statusCode} - ${data}`));
                        return;
                    }
                    
                    const result = JSON.parse(data);
                    console.log('🔍 GPT API原始响应:', JSON.stringify(result, null, 2));
                    
                    const translatedText = result.choices[0].message.content.trim();
                    const translatedLines = translatedText.split('\n');
                    
                    // 解析翻译结果
                    const translatedSegments = segments.map((segment, index) => {
                        if (!segment.text.trim()) {
                            return segment;
                        }
                        
                        // 查找对应的翻译
                        let chineseText = null;
                        for (const line of translatedLines) {
                            if (line.trim().startsWith(`${index + 1}.`)) {
                                chineseText = line.trim().substring(`${index + 1}.`.length).trim();
                                break;
                            }
                        }
                        
                        if (chineseText) {
                            return {
                                ...segment,
                                text: chineseText
                            };
                        } else {
                            console.log(`⚠️ 第${index + 1}条未找到翻译，保留原文`);
                            return segment;
                        }
                    });
                    
                    console.log(`✅ 批量翻译完成，共处理 ${translatedSegments.length} 个字幕段落`);
                    
                    resolve({
                        translatedSegments,
                        gptResponse: {
                            timestamp: Date.now(),
                            data: result
                        }
                    });
                } catch (error) {
                    reject(new Error(`解析GPT响应失败: ${error.message}`));
                }
            });
        });
        
        request.on('error', reject);
        request.setTimeout(60000, () => {
            request.destroy();
            reject(new Error('GPT API请求超时'));
        });
        
        request.write(postData);
        request.end();
    });
}

/**
 * 将segments转换为SRT格式
 */
function convertSegmentsToSrt(segments) {
    let srtContent = '';
    
    segments.forEach((segment, index) => {
        const startTime = formatTime(segment.start);
        const endTime = formatTime(segment.end);
        const text = segment.text.trim();
        
        srtContent += `${index + 1}\n`;
        srtContent += `${startTime} --> ${endTime}\n`;
        srtContent += `${text}\n\n`;
    });
    
    return srtContent;
}

/**
 * 将秒数转换为SRT时间格式 (HH:MM:SS,mmm)
 */
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.floor((seconds % 1) * 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * 主要的音频字幕生成函数
 */
async function generateSubtitle(audioUrl, apiKey, baseUrl, sourceLanguage = 'en', targetLanguage = 'zh') {
    const startTime = Date.now();
    
    try {
        console.log('🎬 开始生成字幕...');
        console.log(`📥 音频链接: ${audioUrl}`);
        console.log(`🌐 源语言: ${sourceLanguage}`);
        
        const needTranslation = sourceLanguage !== targetLanguage;
        console.log(`🔄 处理模式: ${needTranslation ? `${sourceLanguage.toUpperCase()}→${targetLanguage.toUpperCase()}翻译` : '直接转录'}`);
        
        // 1. 下载音频到内存
        const audioBuffer = await downloadAudioToMemory(audioUrl);
        
        // 2. 转录音频
        const { whisperResult, whisperResponse } = await transcribeAudio(audioBuffer, apiKey, baseUrl, sourceLanguage);
        
        let srtContent = '';
        let gptResponse = null;
        
        // 3. 处理翻译
        if (needTranslation && whisperResult.segments) {
            const { translatedSegments, gptResponse: gptResp } = await translateSegmentsBatch(
                whisperResult.segments, 
                apiKey, 
                baseUrl, 
                targetLanguage
            );
            gptResponse = gptResp;
            srtContent = convertSegmentsToSrt(translatedSegments);
        } else {
            // 直接转录模式
            srtContent = convertSegmentsToSrt(whisperResult.segments || []);
        }
        
        // 4. 生成结果
        const processingTime = (Date.now() - startTime) / 1000;
        const timestamp = Date.now();
        
        const result = {
            success: true,
            metadata: {
                audioUrl,
                sourceLanguage,
                targetLanguage,
                needTranslation,
                processingTime,
                timestamp: new Date().toISOString(),
                model: 'whisper-large-v3'
            },
            subtitle: {
                content: srtContent,
                filename: `subtitle_${sourceLanguage}2${targetLanguage}_${timestamp}.srt`,
                segmentCount: whisperResult.segments?.length || 0
            },
            apiResponses: {
                whisper: whisperResponse,
                gpt: gptResponse
            }
        };
        
        console.log(`✅ 字幕生成完成! 耗时: ${processingTime.toFixed(2)}秒`);
        return result;
        
    } catch (error) {
        console.error(`❌ 生成失败: ${error.message}`);
        return {
            success: false,
            error: error.message,
            metadata: {
                audioUrl,
                sourceLanguage,
                targetLanguage,
                processingTime: (Date.now() - startTime) / 1000,
                timestamp: new Date().toISOString()
            }
        };
    }
}

// Serverless函数导出 (支持多种平台)
module.exports = {
    // Vercel格式
    default: async (req, res) => {
        return await handleRequest(req, res);
    },
    
    // AWS Lambda格式
    handler: async (event, context) => {
        const req = {
            method: event.httpMethod || event.requestContext?.http?.method,
            body: event.body,
            query: event.queryStringParameters || {},
            headers: event.headers || {}
        };
        
        const result = await processRequest(req);
        
        return {
            statusCode: result.statusCode,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            body: JSON.stringify(result.body)
        };
    },
    
    // 通用处理函数
    generateSubtitle
};

/**
 * 处理HTTP请求
 */
async function handleRequest(req, res) {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    const result = await processRequest(req);
    res.status(result.statusCode).json(result.body);
}

/**
 * 处理请求逻辑
 */
async function processRequest(req) {
    try {
        if (req.method !== 'POST') {
            return {
                statusCode: 405,
                body: { error: '仅支持POST请求' }
            };
        }
        
        const body = typeof req.body === 'string' ? JSON.parse(req.body) : req.body;
        const { audioUrl, apiKey, baseUrl, sourceLanguage = 'en', targetLanguage = 'zh' } = body;
        
        if (!audioUrl || !apiKey || !baseUrl) {
            return {
                statusCode: 400,
                body: { error: '缺少必要参数: audioUrl, apiKey, baseUrl' }
            };
        }
        
        const result = await generateSubtitle(audioUrl, apiKey, baseUrl, sourceLanguage, targetLanguage);
        
        return {
            statusCode: 200,
            body: result
        };
        
    } catch (error) {
        console.error('请求处理错误:', error);
        return {
            statusCode: 500,
            body: { error: '服务器内部错误', message: error.message }
        };
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    // 测试配置
    const testConfig = {
        audioUrl: "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688b7ad899c624aa84b2560b.mp3",
        apiKey: "sk-OppCJ2Kc3gMqYc0jA79aA759Ec634c649361E90b79BfE72e",
        baseUrl: "https://aihubmix.com",
        sourceLanguage: "en",
        targetLanguage: "zh"
    };
    
    console.log('🧪 开始测试...');
    generateSubtitle(
        testConfig.audioUrl,
        testConfig.apiKey,
        testConfig.baseUrl,
        testConfig.sourceLanguage,
        testConfig.targetLanguage
    ).then(result => {
        console.log('📊 测试结果:', JSON.stringify(result, null, 2));
    }).catch(error => {
        console.error('❌ 测试失败:', error);
    });
}
