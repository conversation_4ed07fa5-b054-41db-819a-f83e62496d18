/**
 * 阿里云IMS字幕烧录测试代码
 * 测试SubmitMediaProducingJob接口的字幕合成功能
 */

const Core = require('@alicloud/pop-core');

// 配置信息
const config = {
  accessKeyId: "LTAI5t5tLkUwyQVg34k3ARFf",
  accessKeySecret: "******************************",
  regionId: "cn-shanghai",
  outputBucket: "video--tanslate"
};

// 测试素材
const testData = {
  videoUrl: "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/video/task_688f00eb862066586c754d79_1754202351081.mp4",
  subtitleUrl: "http://video--tanslate.oss-cn-shanghai.aliyuncs.com/subtitle/task_688f00eb862066586c754d79_translated_1754202381204.srt",
  taskId: "test_" + Date.now()
};

/**
 * 创建IMS客户端
 */
function createIMSClient() {
  return new Core({
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
    endpoint: `https://ice.${config.regionId}.aliyuncs.com`,
    apiVersion: "2020-11-09",
  });
}

/**
 * 提交IMS字幕烧录任务
 */
async function submitSubtitleMergeJob() {
  console.log("🎬 开始测试IMS字幕烧录功能");
  console.log("📹 视频地址:", testData.videoUrl);
  console.log("📝 字幕地址:", testData.subtitleUrl);
  
  try {
    const imsClient = createIMSClient();

    // 构建Timeline配置
    const timeline = {
      VideoTracks: [
        {
          VideoTrackClips: [
            {
              MediaURL: testData.videoUrl,
              TimelineIn: 0
              // TimelineOut 将根据视频实际时长自动计算
            }
          ]
        }
      ],
      SubtitleTracks: [
        {
          SubtitleTrackClips: [
            {
              Type: "Subtitle",
              SubType: "srt",
              FileURL: testData.subtitleUrl,
              // 字体样式配置
              FontSize: 12,
              FontColor: "#FFD700", // 金黄色
              Alignment: "BottomCenter",
              Y: 0.85 // 距离底部15%
            }
          ]
        }
      ]
    };

    // 构建输出配置（保持原视频分辨率）
    const outputMediaConfig = {
      MediaURL: `https://${config.outputBucket}.oss-${config.regionId}.aliyuncs.com/test-output/${testData.taskId}.mp4`
    };

    // 构建IMS请求参数
    const imsParams = {
      Timeline: JSON.stringify(timeline),
      OutputMediaTarget: "oss-object",
      OutputMediaConfig: JSON.stringify(outputMediaConfig),
      UserData: JSON.stringify({
        taskId: testData.taskId,
        action: "burn_subtitle",
        testMode: true
      }),
      Source: "OpenAPI"
    };

    console.log("📋 请求参数:");
    console.log("- Timeline:", JSON.stringify(timeline, null, 2));
    console.log("- 输出地址:", outputMediaConfig.MediaURL);

    // 调用IMS API
    console.log("🚀 正在调用IMS API...");
    const response = await imsClient.request("SubmitMediaProducingJob", imsParams, {
      method: "POST",
    });

    console.log("✅ IMS API调用成功!");
    console.log("📤 响应结果:", JSON.stringify(response, null, 2));

    const { JobId, MediaId, ProjectId } = response;
    
    if (JobId) {
      console.log("🆔 任务信息:");
      console.log("- JobId:", JobId);
      console.log("- MediaId:", MediaId);
      console.log("- ProjectId:", ProjectId);
      
      // 开始轮询任务状态
      console.log("⏳ 开始轮询任务状态...");
      await pollJobStatus(imsClient, JobId);
      
    } else {
      console.error("❌ 响应中缺少JobId");
    }

  } catch (error) {
    console.error("❌ 测试失败:", error);
    if (error.data) {
      console.error("错误详情:", JSON.stringify(error.data, null, 2));
    }
  }
}

/**
 * 轮询任务状态
 */
async function pollJobStatus(imsClient, jobId) {
  const maxAttempts = 60; // 最多轮询60次（30分钟）
  const interval = 30000; // 每30秒查询一次
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔍 第${attempt}次查询任务状态...`);
      
      const response = await imsClient.request("GetMediaProducingJob", {
        JobId: jobId
      }, {
        method: "POST"
      });

      const { MediaProducingJob } = response;
      if (MediaProducingJob) {
        const { Status, CompleteTime, MediaURL, MediaId, ErrorMessage } = MediaProducingJob;
        
        console.log(`📊 任务状态: ${Status}`);
        
        if (Status === 'Success') {
          console.log("🎉 任务完成!");
          console.log("📹 输出视频地址:", MediaURL);
          console.log("🆔 媒资ID:", MediaId);
          console.log("⏰ 完成时间:", CompleteTime);
          return;
          
        } else if (Status === 'Failed') {
          console.error("❌ 任务失败!");
          console.error("错误信息:", ErrorMessage);
          return;
          
        } else if (Status === 'Processing') {
          console.log("⏳ 任务处理中，等待30秒后继续查询...");
          
        } else {
          console.log(`📋 任务状态: ${Status}`);
        }
        
      } else {
        console.error("❌ 无法获取任务信息");
        return;
      }
      
      // 等待30秒后继续查询
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
      }
      
    } catch (error) {
      console.error(`❌ 查询任务状态失败 (第${attempt}次):`, error.message);
      
      // 等待后重试
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }
  }
  
  console.log("⏰ 轮询超时，请手动检查任务状态");
}

/**
 * 测试字幕文件是否可访问
 */
async function testSubtitleFile() {
  console.log("🔍 测试字幕文件可访问性...");
  
  try {
    const https = require('https');
    const http = require('http');
    
    const client = testData.subtitleUrl.startsWith('https') ? https : http;
    
    return new Promise((resolve, reject) => {
      const req = client.get(testData.subtitleUrl, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          if (res.statusCode === 200) {
            console.log("✅ 字幕文件可访问");
            console.log("📝 字幕内容预览:");
            console.log(data.substring(0, 200) + "...");
            resolve(true);
          } else {
            console.error(`❌ 字幕文件访问失败，状态码: ${res.statusCode}`);
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        });
      });
      
      req.on('error', (error) => {
        console.error("❌ 字幕文件访问错误:", error.message);
        reject(error);
      });
      
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('请求超时'));
      });
    });
    
  } catch (error) {
    console.error("❌ 测试字幕文件失败:", error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log("🚀 开始IMS字幕烧录测试");
  console.log("=" * 50);
  
  try {
    // 1. 测试字幕文件可访问性
    await testSubtitleFile();
    
    console.log("\n" + "=" * 50);
    
    // 2. 提交字幕烧录任务
    await submitSubtitleMergeJob();
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error);
  }
  
  console.log("\n🏁 测试完成");
}

// 运行测试
if (require.main === module) {
  runTest();
}

module.exports = {
  runTest,
  submitSubtitleMergeJob,
  pollJobStatus,
  testSubtitleFile
};
