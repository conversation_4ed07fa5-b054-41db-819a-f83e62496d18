// uniCloud云函数：处理视频任务核心逻辑
"use strict";

const Core = require("@alicloud/pop-core");
const createConfig = require("uni-config-center");

/**
 * 处理视频任务的核心逻辑
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.ossUrl - 视频OSS地址（用于音频提取）
 * @param {string} event.audioOssUrl - 音频文件地址（用于语音识别）
 * @param {string} event.action - 执行动作：extract_audio, speech_recognition, translate, merge_subtitle
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, ossUrl, audioOssUrl, action } = event;

    console.log("process-video-task 云函数被调用，参数：", { taskId, ossUrl, audioOssUrl, action });

    // 参数验证
    if (!taskId) {
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    if (!action) {
      return {
        code: 400,
        message: "缺少必要参数：action",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 根据动作执行相应的处理
    let result = null;
    switch (action) {
      case "extract_audio":
        if (!ossUrl) {
          throw new Error("音频提取需要ossUrl参数");
        }
        result = await extractAudio(taskId, ossUrl, tasksCollection);
        break;

      case "speech_recognition":
        if (!audioOssUrl) {
          throw new Error("Whisper语音识别需要audioOssUrl参数");
        }
        result = await speechRecognitionWhisper(taskId, audioOssUrl, tasksCollection);
        break;

      case "translate":
        result = await translateSubtitleGPT(taskId, tasksCollection);
        break;

      case "merge_subtitle":
        result = await mergeSubtitle(taskId, tasksCollection);
        break;

      case "upload_srt":
        if (!event.srtContent) {
          throw new Error("上传SRT需要srtContent参数");
        }
        result = await uploadSrtAndProceed(taskId, event.srtContent, tasksCollection);
        break;

      // handle_paraformer_success action 已移除，现在使用 Whisper 进行语音识别

      default:
        throw new Error("不支持的动作：" + action);
    }

    return {
      code: 200,
      message: "任务处理成功",
      data: result,
    };
  } catch (error) {
    console.error("process-video-task 云函数执行错误：", error);

    // 更新任务状态为失败
    if (event.taskId) {
      try {
        const db = uniCloud.database();
        await db.collection("tasks").doc(event.taskId).update({
          status: "failed",
          errorMessage: error.message,
          updateTime: new Date(),
        });
      } catch (updateError) {
        console.error("更新任务失败状态时出错：", updateError);
      }
    }

    return {
      code: 500,
      message: "任务处理失败: " + error.message,
    };
  }
};

/**
 * 提取音频功能
 * @param {string} taskId - 任务ID
 * @param {string} ossUrl - 视频OSS地址
 * @param {Object} tasksCollection - 任务集合
 */
async function extractAudio(taskId, ossUrl, tasksCollection) {
  console.log("开始音频提取，taskId：", taskId, "，ossUrl：", ossUrl);

  // 验证任务是否存在且状态正确
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  if (taskInfo.data[0].status !== "extracting_audio") {
    console.warn("任务状态不正确，当前状态：", taskInfo.data[0].status);
  }

  // 获取阿里云MPS配置
  const aliyunConfig = createConfig({
    pluginId: "aliyun-mps",
    defaultConfig: {
      regionId: "cn-shanghai",
    },
  });

  const accessKeyId = aliyunConfig.config("accessKeyId");
  const accessKeySecret = aliyunConfig.config("accessKeySecret");
  const regionId = aliyunConfig.config("regionId");
  const pipelineId = aliyunConfig.config("pipelineId");
  const inputBucket = aliyunConfig.config("inputBucket");
  const outputBucket = aliyunConfig.config("outputBucket");
  const audioTemplateId = aliyunConfig.config("audioTemplateId");

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("阿里云MPS配置错误，请配置访问密钥");
  }

  if (!pipelineId || !inputBucket || !outputBucket) {
    throw new Error("阿里云MPS配置不完整，请配置pipelineId、inputBucket和outputBucket");
  }

  console.log("MPS配置检查通过，regionId：", regionId, "，pipelineId：", pipelineId);

  // 创建MPS客户端
  const mpsClient = new Core({
    accessKeyId,
    accessKeySecret,
    endpoint: `https://mts.${regionId}.aliyuncs.com`,
    apiVersion: "2014-06-18",
  });

  // 从OSS URL中解析bucket和object信息
  const ossUrlParts = ossUrl.match(/https:\/\/([^.]+)\.([^.]+)\.aliyuncs\.com\/(.+)/);
  if (!ossUrlParts) {
    throw new Error("无效的OSS URL格式: " + ossUrl);
  }

  const [, bucketName, region, objectPath] = ossUrlParts;
  console.log("解析OSS URL:", { bucketName, region, objectPath });

  // 构建MPS转码参数 - 仅提取音频为MP3格式
  const mpsParams = {
    Input: JSON.stringify({
      Location: `oss-${regionId}`,
      Bucket: bucketName,
      Object: objectPath, // 直接使用OSS中的文件路径
    }),
    OutputBucket: outputBucket,
    OutputLocation: `oss-${regionId}`,
    Outputs: JSON.stringify([
      {
        OutputObject: `audio/${taskId}.mp3`,
        TemplateId: audioTemplateId || "S00000001-100010", // 使用配置的模板ID或默认音频模板
        UserData: JSON.stringify({ taskId }),
        // 确保音频提取的容器格式正确
        Container: JSON.stringify({
          Format: "mp3",
        }),
        // 明确指定只保留音频流，移除视频流
        Video: JSON.stringify({
          Remove: "true",
        }),
      },
    ]),
    PipelineId: pipelineId,
  };

  console.log("调用MPS SubmitJobs API，参数：", mpsParams);

  // 调用MPS API提交转码任务
  const mpsResponse = await mpsClient.request("SubmitJobs", mpsParams, {
    method: "POST",
  });

  console.log("MPS API 响应：", mpsResponse);

  const { JobResultList } = mpsResponse;
  if (JobResultList && JobResultList.JobResult && JobResultList.JobResult.length > 0) {
    const jobResult = JobResultList.JobResult[0];
    const { Job } = jobResult;

    if (Job && Job.JobId) {
      console.log("音频提取任务提交成功，JobId：", Job.JobId);
      // 更新任务记录，保存音频提取JobId
      await tasksCollection.doc(taskId).update({
        audioExtractionJobId: Job.JobId,
        updateTime: new Date(),
      });

      return {
        jobId: Job.JobId,
        status: "submitted",
      };
    } else {
      throw new Error("MPS任务提交失败：" + JSON.stringify(jobResult));
    }
  } else {
    throw new Error("MPS API响应格式错误");
  }
}

/**
 * 从OSS下载SRT文件内容
 * @param {string} ossUrl - OSS文件地址
 * @returns {Promise<string>} SRT文件内容
 */
async function downloadSrtFromOss(ossUrl) {
  const https = require("https");
  const http = require("http");
  const url = require("url");

  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(ossUrl);
    const client = parsedUrl.protocol === "https:" ? https : http;

    const req = client.get(ossUrl, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode === 200) {
          resolve(data);
        } else {
          reject(new Error(`下载SRT文件失败，状态码: ${res.statusCode}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`下载SRT文件失败: ${error.message}`));
    });
  });
}

/**
 * 合并字幕功能 - 使用阿里云MPS进行字幕烧录
 * @param {string} taskId - 任务ID
 * @param {Object} tasksCollection - 任务集合
 */
async function mergeSubtitle(taskId, tasksCollection) {
  console.log("🎬 [字幕烧录] 步骤1: 开始字幕烧录流程，taskId：", taskId);

  try {
    console.log("🎬 [字幕烧录] 步骤2: 获取任务信息");
    // 获取任务信息
    const taskInfo = await tasksCollection.doc(taskId).get();
    if (!taskInfo.data || taskInfo.data.length === 0) {
      throw new Error("任务不存在");
    }

    const task = taskInfo.data[0];
    const { ossUrl, subtitleOssUrl } = task;

    console.log("🎬 [字幕烧录] 步骤3: 任务数据获取成功");
    console.log("📋 任务详细信息：", JSON.stringify({
      taskId: task._id,
      status: task.status,
      ossUrl: ossUrl,
      subtitleOssUrl: subtitleOssUrl,
      fileName: task.fileName
    }, null, 2));

    console.log("🎬 [字幕烧录] 步骤4: 验证必要参数");
    if (!ossUrl) {
      console.error("❌ [字幕烧录] 错误: 缺少原始视频文件地址");
      throw new Error("缺少原始视频文件地址");
    }

    if (!subtitleOssUrl) {
      console.error("❌ [字幕烧录] 错误: 缺少字幕文件地址");
      throw new Error("缺少字幕文件地址");
    }

    console.log("✅ [字幕烧录] 步骤5: 参数验证通过");
    console.log("📹 原视频文件：", ossUrl);
    console.log("📝 字幕文件：", subtitleOssUrl);

    console.log("🎬 [字幕烧录] 步骤6: 获取阿里云MPS配置");
    // 获取阿里云MPS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-mps",
      defaultConfig: {
        regionId: "cn-shanghai",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const regionId = aliyunConfig.config("regionId");
    const pipelineId = aliyunConfig.config("pipelineId");
    const outputBucket = aliyunConfig.config("outputBucket");
    const videoTemplateId = aliyunConfig.config("videoTemplateId"); // 视频转码模板ID

    console.log("🔧 [字幕烧录] MPS配置信息：", {
      regionId,
      pipelineId,
      outputBucket,
      videoTemplateId,
      hasAccessKeyId: !!accessKeyId,
      hasAccessKeySecret: !!accessKeySecret
    });

    if (!accessKeyId || !accessKeySecret) {
      console.error("❌ [字幕烧录] 错误: 阿里云MPS配置错误，缺少访问密钥");
      throw new Error("阿里云MPS配置错误，请配置访问密钥");
    }

    if (!pipelineId || !outputBucket) {
      console.error("❌ [字幕烧录] 错误: MPS配置不完整", { pipelineId, outputBucket });
      throw new Error("阿里云MPS配置不完整，请配置pipelineId和outputBucket");
    }

    console.log("✅ [字幕烧录] 步骤8: MPS配置检查通过");

    console.log("🎬 [字幕烧录] 步骤9: 创建MPS客户端");
    // 创建MPS客户端
    const mpsClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://mts.${regionId}.aliyuncs.com`,
      apiVersion: "2014-06-18",
    });

    console.log("✅ [字幕烧录] MPS客户端创建成功");

    console.log("🎬 [字幕烧录] 步骤10: 解析原视频OSS URL");
    // 解析原视频OSS URL
    const videoOssUrlParts = ossUrl.match(/https:\/\/([^.]+)\.([^.]+)\.aliyuncs\.com\/(.+)/);
    if (!videoOssUrlParts) {
      console.error("❌ [字幕烧录] 错误: 无效的视频OSS URL格式:", ossUrl);
      throw new Error("无效的视频OSS URL格式: " + ossUrl);
    }

    const [, videoBucketName, videoRegion, videoObjectPath] = videoOssUrlParts;
    console.log("✅ [字幕烧录] 视频OSS URL解析成功:", {
      videoBucketName,
      videoRegion,
      videoObjectPath
    });

    console.log("🎬 [字幕烧录] 步骤11: 解析字幕OSS URL");
    // 解析字幕OSS URL
    const subtitleOssUrlParts = subtitleOssUrl.match(
      /https?:\/\/([^.]+)\.([^.]+)\.aliyuncs\.com\/(.+)/
    );
    if (!subtitleOssUrlParts) {
      console.error("❌ [字幕烧录] 错误: 无效的字幕OSS URL格式:", subtitleOssUrl);
      throw new Error("无效的字幕OSS URL格式: " + subtitleOssUrl);
    }

    const [, subtitleBucketName, subtitleRegion, subtitleObjectPath] = subtitleOssUrlParts;
    console.log("✅ [字幕烧录] 字幕OSS URL解析成功:", {
      subtitleBucketName,
      subtitleRegion,
      subtitleObjectPath
    });

    console.log("🎬 [字幕烧录] 步骤12: 验证解析结果");
    // 验证解析结果
    if (!subtitleBucketName || !subtitleObjectPath) {
      console.error("❌ [字幕烧录] 错误: 字幕OSS URL解析失败", {
        subtitleBucketName,
        subtitleObjectPath,
        originalUrl: subtitleOssUrl
      });
      throw new Error(
        `字幕OSS URL解析失败: Bucket=${subtitleBucketName}, Object=${subtitleObjectPath}, URL=${subtitleOssUrl}`
      );
    }

    console.log("✅ [字幕烧录] 解析结果验证通过");

    console.log("🎬 [字幕烧录] 步骤13: 构建MPS字幕烧录参数");
    // 构建MPS字幕烧录参数
    const mpsParams = {
      Input: JSON.stringify({
        Location: `oss-${regionId}`,
        Bucket: videoBucketName,
        Object: videoObjectPath,
      }),
      OutputBucket: outputBucket,
      OutputLocation: `oss-${regionId}`,
      Outputs: JSON.stringify([
        {
          OutputObject: `final/${taskId}.mp4`,
          TemplateId: videoTemplateId || "d963d203a57042d9a49c730fc61d2b44", // 使用配置的视频模板ID或默认视频模板
          UserData: JSON.stringify({
            taskId: taskId,
            action: "burn_subtitle",
          }),
          // 字幕配置应该在这里，按照官方文档的正确结构
          SubtitleConfig: JSON.stringify({
            ExtSubtitleList: [
              {
                Input: {
                  Location: `oss-${regionId}`,
                  Bucket: subtitleBucketName,
                  Object: subtitleObjectPath,
                },
                CharEnc: "UTF-8", // 字符编码
                FontSize: 4,
              },
            ],
          }),
        },
      ]),
      PipelineId: pipelineId,
    };

    console.log("📋 [字幕烧录] MPS参数构建完成:", {
      inputBucket: videoBucketName,
      inputObject: videoObjectPath,
      outputBucket: outputBucket,
      outputObject: `final/${taskId}.mp4`,
      subtitleBucket: subtitleBucketName,
      subtitleObject: subtitleObjectPath,
      pipelineId: pipelineId
    });

    console.log("🎬 [字幕烧录] 步骤14: 提交MPS字幕烧录任务");
    console.log("📤 [字幕烧录] 提交参数详情：", JSON.stringify(mpsParams, null, 2));

    // 额外调试：显示解析后的Outputs内容
    const outputsObj = JSON.parse(mpsParams.Outputs);
    console.log("📋 [字幕烧录] Outputs对象详情：", JSON.stringify(outputsObj, null, 2));

    console.log("🚀 [字幕烧录] 正在调用MPS API...");
    // 调用MPS API提交字幕烧录任务
    const mpsResponse = await mpsClient.request("SubmitJobs", mpsParams, {
      method: "POST",
    });

    console.log("✅ [字幕烧录] 步骤15: MPS API调用成功");
    console.log("📨 [字幕烧录] MPS响应详情：", JSON.stringify(mpsResponse, null, 2));

    console.log("🎬 [字幕烧录] 步骤16: 解析MPS响应");
    const { JobResultList } = mpsResponse;

    if (JobResultList && JobResultList.JobResult && JobResultList.JobResult.length > 0) {
      const jobResult = JobResultList.JobResult[0];
      console.log("📋 [字幕烧录] JobResult详情：", JSON.stringify(jobResult, null, 2));

      const { Job } = jobResult;

      if (Job && Job.JobId) {
        console.log("✅ [字幕烧录] 步骤17: 字幕烧录任务提交成功");
        console.log("🆔 [字幕烧录] MPS JobId：", Job.JobId);

        console.log("🎬 [字幕烧录] 步骤18: 更新任务记录，保存字幕烧录JobId");
        // 更新任务记录，保存字幕烧录JobId，并确保状态为 merging
        await tasksCollection.doc(taskId).update({
          subtitleMergeJobId: Job.JobId,
          status: "merging", // 确保状态正确
          updateTime: new Date(),
        });

        console.log("🔍 [字幕烧录] 验证：任务状态已设置为 merging，JobId:", Job.JobId);

        console.log("✅ [字幕烧录] 步骤19: 任务记录更新完成");
        console.log("🎉 [字幕烧录] 字幕烧录流程提交完成，等待MPS处理");

        return {
          jobId: Job.JobId,
          status: "submitted",
          message: "字幕烧录任务提交成功",
        };
      } else {
        console.error("❌ [字幕烧录] 错误: MPS响应中缺少JobId", { jobResult });
        throw new Error("MPS字幕烧录任务提交失败：" + JSON.stringify(jobResult));
      }
    } else {
      console.error("❌ [字幕烧录] 错误: MPS API响应格式错误", {
        hasJobResultList: !!JobResultList,
        hasJobResult: !!(JobResultList && JobResultList.JobResult),
        jobResultLength: JobResultList && JobResultList.JobResult ? JobResultList.JobResult.length : 0
      });
      throw new Error("MPS API响应格式错误");
    }
  } catch (error) {
    console.error("❌ [字幕烧录] 致命错误: 字幕烧录流程失败");
    console.error("❌ [字幕烧录] 错误详情：", error);
    console.error("❌ [字幕烧录] 错误堆栈：", error.stack);

    console.log("🎬 [字幕烧录] 步骤X: 更新任务状态为失败");
    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "字幕烧录失败：" + error.message,
      updateTime: new Date(),
    });

    console.log("❌ [字幕烧录] 流程结束: 任务已标记为失败");
    throw error;
  }
}

/**
 * 解析SRT字幕格式
 * @param {string} srtContent - SRT字幕内容
 * @returns {Array} 解析后的字幕条目数组
 */
function parseSRT(srtContent) {
  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n");

      entries.push({
        index,
        timeRange,
        text: text.trim(),
      });
    }
  }

  return entries;
}

/**
 * 生成SRT字幕格式
 * @param {Array} entries - 字幕条目数组
 * @returns {string} SRT格式字符串
 */
function generateSRT(entries) {
  return entries
    .map((entry) => {
      return `${entry.index}\n${entry.timeRange}\n${entry.text}\n`;
    })
    .join("\n");
}

/**
 * 上传SRT文件并继续下一步处理（翻译）
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - SRT内容
 * @param {Object} tasksCollection - 任务集合
 */
async function uploadSrtAndProceed(taskId, srtContent, tasksCollection) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_${timestamp}.srt`;

    // 上传SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传SRT文件完成，地址：", subtitleOssUrl);

    // 更新任务状态为translating，准备翻译
    await tasksCollection.doc(taskId).update({
      status: "translating",
      subtitleOssUrl: subtitleOssUrl,
      updateTime: new Date(),
    });

    // 检查任务是否处于后台模式
    const taskInfo = await tasksCollection.doc(taskId).get();
    const isBackgroundMode = taskInfo.data && taskInfo.data[0] && taskInfo.data[0].backgroundMode;

    if (isBackgroundMode) {
      // 后台模式：通过task-scheduler自动处理
      console.log(`任务 ${taskId} 处于后台模式，将由调度器自动处理翻译`);
      return {
        status: "translating",
        message: "已进入翻译阶段，后台处理中",
        subtitleOssUrl: subtitleOssUrl,
      };
    } else {
      // 前台模式：直接调用翻译功能
      const translateResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: {
          taskId: taskId,
          action: "translate",
        },
      });

      console.log("调用翻译功能结果：", translateResult);
      return translateResult.result;
    }
  } catch (error) {
    console.error("上传SRT文件失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "上传字幕文件失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 上传翻译后的SRT文件并进行字幕烧录
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - 翻译后的SRT内容
 * @param {Object} tasksCollection - 任务集合
 */
async function uploadTranslatedSrtAndMerge(taskId, srtContent, tasksCollection) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成翻译后SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.srt`;

    // 上传翻译后的SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传翻译后SRT文件完成，地址：", subtitleOssUrl);

    // 更新任务状态为merging，准备字幕烧录
    await tasksCollection.doc(taskId).update({
      status: "merging",
      subtitleOssUrl: subtitleOssUrl,
      updateTime: new Date(),
    });

    console.log("🔄 翻译完成，准备启动字幕烧录");

    // 检查任务是否处于后台模式
    const taskInfo = await tasksCollection.doc(taskId).get();
    const isBackgroundMode = taskInfo.data && taskInfo.data[0] && taskInfo.data[0].backgroundMode;

    // 无论前台还是后台模式，都尝试立即启动字幕烧录
    try {
      console.log(`任务 ${taskId} 开始启动字幕烧录`);
      const mergeResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: {
          taskId: taskId,
          action: "merge_subtitle",
        },
      });

      console.log("字幕烧录启动结果：", mergeResult.result);
      return mergeResult.result;
    } catch (error) {
      console.error("字幕烧录启动失败：", error.message);

      if (isBackgroundMode) {
        // 后台模式：启动失败时返回成功，让调度器后续处理
        console.log(`任务 ${taskId} 处于后台模式，字幕烧录启动失败，将由调度器重试`);
        return {
          status: "merging",
          message: "已进入字幕烧录阶段，后台处理中",
          subtitleOssUrl: subtitleOssUrl,
        };
      } else {
        // 前台模式：抛出错误
        throw error;
      }
    }
  } catch (error) {
    console.error("上传翻译后SRT文件失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "上传翻译后字幕文件失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 使用Whisper进行语音识别
 * @param {string} taskId - 任务ID
 * @param {string} audioOssUrl - 音频文件地址
 * @param {Object} tasksCollection - 任务集合
 */
async function speechRecognitionWhisper(taskId, audioOssUrl, tasksCollection) {
  console.log("开始Whisper语音识别，taskId：", taskId, "，audioOssUrl：", audioOssUrl);

  // 验证任务是否存在且状态正确
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  if (taskInfo.data[0].status !== "recognizing") {
    console.warn("任务状态不正确，当前状态：", taskInfo.data[0].status);
  }

  try {
    // 获取任务的源语言设置
    const taskData = taskInfo.data[0];
    const sourceLanguage = taskData.sourceLanguage || "auto";

    // 调用新的Whisper识别云函数
    const recognitionResult = await uniCloud.callFunction({
      name: "speech-recognition-whisper",
      data: {
        taskId: taskId,
        audioOssUrl: audioOssUrl,
        sourceLanguage: sourceLanguage,
      },
    });

    console.log("Whisper识别云函数调用结果：", recognitionResult);

    if (recognitionResult.result && recognitionResult.result.code === 200) {
      return {
        status: "completed",
        message: "Whisper语音识别成功",
        data: recognitionResult.result.data,
      };
    } else {
      throw new Error("Whisper识别失败：" + (recognitionResult.result?.message || "未知错误"));
    }
  } catch (error) {
    console.error("Whisper识别调用失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "Whisper语音识别失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 使用GPT进行字幕翻译
 * @param {string} taskId - 任务ID
 * @param {Object} tasksCollection - 任务集合
 */
async function translateSubtitleGPT(taskId, tasksCollection) {
  console.log("🔄 开始GPT字幕翻译，taskId：", taskId);

  try {
    // 获取任务信息
    const taskInfo = await tasksCollection.doc(taskId).get();
    if (!taskInfo.data || taskInfo.data.length === 0) {
      throw new Error("任务不存在");
    }

    const task = taskInfo.data[0];
    const { sourceLanguage = "auto", targetLanguage = "zh" } = task;

    console.log("📋 任务信息：", {
      taskId,
      status: task.status,
      sourceLanguage,
      targetLanguage,
      hasSubtitleUrl: !!task.subtitleOssUrl
    });

    // 确保任务状态正确
    if (task.status !== "translating") {
      console.warn("⚠️ 任务状态不正确，当前状态：", task.status, "，更新为translating");
      await tasksCollection.doc(taskId).update({
        status: "translating",
        updateTime: new Date(),
      });
    }

    // 设置超时保护 - 5分钟超时
    const TRANSLATION_TIMEOUT = 300000; // 5分钟
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`翻译超时（${TRANSLATION_TIMEOUT/1000}秒）`));
      }, TRANSLATION_TIMEOUT);
    });

    console.log("📡 调用GPT翻译云函数...");
    const translationPromise = uniCloud.callFunction({
      name: "subtitle-translation-gpt",
      data: {
        taskId: taskId,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      },
    });

    // 使用Promise.race实现超时控制
    const translationResult = await Promise.race([
      translationPromise,
      timeoutPromise
    ]);

    console.log("✅ GPT翻译云函数调用完成：", {
      code: translationResult.result?.code,
      message: translationResult.result?.message,
      hasData: !!translationResult.result?.data
    });

    if (translationResult.result && translationResult.result.code === 200) {
      return {
        status: "completed",
        message: "GPT字幕翻译成功",
        data: translationResult.result.data,
      };
    } else {
      throw new Error("GPT翻译失败：" + (translationResult.result?.message || "未知错误"));
    }
  } catch (error) {
    console.error("❌ GPT翻译调用失败：", {
      error: error.message,
      stack: error.stack,
      taskId
    });

    // 更新任务状态为失败，并重置翻译启动标记
    try {
      await tasksCollection.doc(taskId).update({
        status: "failed",
        errorMessage: "GPT字幕翻译失败：" + error.message,
        translationStarted: false, // 重置翻译启动标记
        updateTime: new Date(),
      });
      console.log("📝 任务状态已更新为failed");
    } catch (updateError) {
      console.error("❌ 更新任务状态失败：", updateError);
    }

    throw error;
  }
}
